<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- 本地Bootstrap CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}">
    <!-- 本地Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome-free/css/all.min.css') }}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .school-name {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .date-info {
            font-size: 16px;
            color: #6c757d;
            font-weight: 500;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: #667eea;
            font-size: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .required-mark {
            color: #e74c3c;
            font-weight: bold;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .rating-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .rating-group {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .rating-group:hover {
            transform: translateY(-2px);
        }

        .rating-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .rating-container {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-bottom: 10px;
        }

        .rating-star {
            font-size: 28px;
            color: #ddd;
            cursor: pointer;
            transition: all 0.2s ease;
            text-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .rating-star:hover {
            transform: scale(1.1);
        }

        .rating-star.active {
            color: #ffd700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .rating-desc {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .photo-upload-area {
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .photo-upload-area:hover {
            border-color: #5a6fd8;
            background: linear-gradient(135deg, #f0f5ff 0%, #ddeeff 100%);
        }

        .photo-upload-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }

        .photo-preview {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 10px;
            margin: 5px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .photo-preview:hover {
            transform: scale(1.05);
        }

        .submit-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .submit-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            color: white;
        }

        .back-btn {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background: #667eea;
            color: white;
            transform: translateX(-3px);
        }

        .footer {
            text-align: center;
            color: rgba(255,255,255,0.8);
            font-size: 14px;
            margin-top: 30px;
        }

        .progress-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .progress-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            color: white;
            position: relative;
        }

        .progress-step.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .progress-step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #ddd;
            transform: translateY(-50%);
        }

        .progress-step:last-child::after {
            display: none;
        }

        @media (max-width: 768px) {
            .form-card {
                padding: 20px;
                margin: 0 10px;
            }

            .form-section {
                padding: 20px;
            }

            .school-name {
                font-size: 22px;
            }

            .rating-star {
                font-size: 24px;
            }

            .photo-preview {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 返回按钮 -->
        <a href="{{ url_for('daily_management.companion_entry', school_id=school.id) }}" class="back-btn">
            <i class="fas fa-arrow-left me-2"></i>返回首页
        </a>

        <div class="form-card">
            <!-- 页面头部 -->
            <div class="header">
                <div class="school-name">{{ school.name }}</div>
                <div class="date-info">添加陪餐记录 - {{ log.log_date }}</div>
            </div>

            <!-- 进度指示器 -->
            <div class="progress-indicator">
                <div class="progress-step active">1</div>
                <div class="progress-step active">2</div>
                <div class="progress-step active">3</div>
            </div>

            <form method="post" action="{{ url_for('daily_management.public_submit_companion', log_id=log.id) }}" enctype="multipart/form-data" id="companionForm">
                {{ form.csrf_token }}

                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-user"></i>
                        基本信息
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="companion_name" class="form-label">陪餐人姓名 <span class="required-mark">*</span></label>
                            <input type="text" class="form-control" id="companion_name" name="companion_name" required placeholder="请输入您的姓名">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="companion_role" class="form-label">陪餐人角色 <span class="required-mark">*</span></label>
                            <select class="form-select" id="companion_role" name="companion_role" required>
                                <option value="">请选择您的角色</option>
                                <option value="校长">校长</option>
                                <option value="副校长">副校长</option>
                                <option value="主任">主任</option>
                                <option value="教师">教师</option>
                                <option value="家长">家长</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 陪餐信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-clock"></i>
                        陪餐信息
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="meal_type" class="form-label">餐次 <span class="required-mark">*</span></label>
                            <select class="form-select" id="meal_type" name="meal_type" required>
                                <option value="">请选择餐次</option>
                                <option value="breakfast">早餐</option>
                                <option value="lunch">午餐</option>
                                <option value="dinner">晚餐</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="dining_date" class="form-label">陪餐日期 <span class="required-mark">*</span></label>
                            <input type="date" class="form-control" id="dining_date" name="dining_date" value="{{ log.log_date|format_datetime('%Y-%m-%d') }}" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="dining_time" class="form-label">陪餐时间 <span class="required-mark">*</span></label>
                            <input type="time" class="form-control" id="dining_time" name="dining_time" value="12:00" required>
                        </div>
                    </div>
                </div>

                <!-- 评分部分 -->
                <div class="rating-section">
                    <div class="section-title">
                        <i class="fas fa-star"></i>
                        评价评分
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="rating-group">
                                <div class="rating-label">
                                    <i class="fas fa-utensils text-primary me-2"></i>口味评分
                                </div>
                                <div class="rating-container" id="taste-rating">
                                    <span class="rating-star" data-value="1">★</span>
                                    <span class="rating-star" data-value="2">★</span>
                                    <span class="rating-star" data-value="3">★</span>
                                    <span class="rating-star" data-value="4">★</span>
                                    <span class="rating-star" data-value="5">★</span>
                                </div>
                                <div class="rating-desc">点击星星进行评分</div>
                                <input type="hidden" name="taste_rating" id="taste_rating_input" value="0">
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="rating-group">
                                <div class="rating-label">
                                    <i class="fas fa-broom text-success me-2"></i>卫生评分
                                </div>
                                <div class="rating-container" id="hygiene-rating">
                                    <span class="rating-star" data-value="1">★</span>
                                    <span class="rating-star" data-value="2">★</span>
                                    <span class="rating-star" data-value="3">★</span>
                                    <span class="rating-star" data-value="4">★</span>
                                    <span class="rating-star" data-value="5">★</span>
                                </div>
                                <div class="rating-desc">点击星星进行评分</div>
                                <input type="hidden" name="hygiene_rating" id="hygiene_rating_input" value="0">
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="rating-group">
                                <div class="rating-label">
                                    <i class="fas fa-concierge-bell text-warning me-2"></i>服务评分
                                </div>
                                <div class="rating-container" id="service-rating">
                                    <span class="rating-star" data-value="1">★</span>
                                    <span class="rating-star" data-value="2">★</span>
                                    <span class="rating-star" data-value="3">★</span>
                                    <span class="rating-star" data-value="4">★</span>
                                    <span class="rating-star" data-value="5">★</span>
                                </div>
                                <div class="rating-desc">点击星星进行评分</div>
                                <input type="hidden" name="service_rating" id="service_rating_input" value="0">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 意见建议 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-comment"></i>
                        意见建议
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="comments" class="form-label">评价意见</label>
                            <textarea class="form-control" id="comments" name="comments" rows="4" placeholder="请分享您对食堂的整体评价..."></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="suggestions" class="form-label">改进建议</label>
                            <textarea class="form-control" id="suggestions" name="suggestions" rows="4" placeholder="请提出您的改进建议..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- 照片上传 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-camera"></i>
                        照片上传
                    </div>
                    <div class="photo-upload-area" onclick="document.getElementById('photos').click()">
                        <div class="photo-upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h5>点击上传照片</h5>
                        <p class="text-muted">支持 JPG、JPEG、PNG 格式，可选择多张照片</p>
                        <input class="d-none" type="file" id="photos" name="photos" multiple accept="image/*">
                    </div>
                    <div id="photo-previews" class="mt-3 d-flex flex-wrap justify-content-center"></div>
                </div>

                <!-- 提交按钮 -->
                <div class="text-center mt-4">
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-paper-plane me-2"></i>提交陪餐记录
                    </button>
                </div>
            </form>
        </div>

        <div class="footer">
            <p>{{ school.name }} 食堂管理系统 &copy; {{ today.year }}</p>
            <p>感谢您的参与，让我们共同营造更好的用餐环境</p>
        </div>
    </div>

    <!-- 本地Bootstrap JS -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <script>
        // 星级评分增强版
        function setupRating(containerId, inputId) {
            const container = document.getElementById(containerId);
            const input = document.getElementById(inputId);
            const stars = container.querySelectorAll('.rating-star');

            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    const value = parseInt(this.getAttribute('data-value'));
                    input.value = value;

                    // 添加点击动画
                    this.style.transform = 'scale(1.3)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 200);

                    // 更新星星显示
                    updateStars(stars, value);
                });

                star.addEventListener('mouseover', function() {
                    const value = parseInt(this.getAttribute('data-value'));
                    updateStars(stars, value);
                });

                container.addEventListener('mouseleave', function() {
                    const value = parseInt(input.value);
                    updateStars(stars, value);
                });
            });
        }

        function updateStars(stars, value) {
            stars.forEach(s => {
                if (parseInt(s.getAttribute('data-value')) <= value) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        }

        // 照片预览增强版
        document.getElementById('photos').addEventListener('change', function(e) {
            const previewsDiv = document.getElementById('photo-previews');
            previewsDiv.innerHTML = '';

            if (this.files.length > 0) {
                // 显示上传的文件数量
                const fileCount = document.createElement('div');
                fileCount.className = 'w-100 text-center mb-3';
                fileCount.innerHTML = `<small class="text-muted">已选择 ${this.files.length} 张照片</small>`;
                previewsDiv.appendChild(fileCount);
            }

            for (const file of this.files) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const photoContainer = document.createElement('div');
                    photoContainer.className = 'position-relative d-inline-block';

                    const img = document.createElement('img');
                    img.src = event.target.result;
                    img.className = 'photo-preview';
                    img.style.opacity = '0';
                    img.style.transform = 'scale(0.8)';

                    // 添加删除按钮
                    const deleteBtn = document.createElement('button');
                    deleteBtn.type = 'button';
                    deleteBtn.className = 'btn btn-danger btn-sm position-absolute';
                    deleteBtn.style.top = '5px';
                    deleteBtn.style.right = '5px';
                    deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
                    deleteBtn.onclick = function() {
                        photoContainer.remove();
                    };

                    photoContainer.appendChild(img);
                    photoContainer.appendChild(deleteBtn);
                    previewsDiv.appendChild(photoContainer);

                    // 添加淡入动画
                    setTimeout(() => {
                        img.style.transition = 'all 0.3s ease';
                        img.style.opacity = '1';
                        img.style.transform = 'scale(1)';
                    }, 100);
                }
                reader.readAsDataURL(file);
            }
        });

        // 表单验证
        function validateForm() {
            const requiredFields = ['companion_name', 'companion_role', 'meal_type', 'dining_date', 'dining_time'];
            let isValid = true;

            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // 表单提交处理
        document.getElementById('companionForm').addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                alert('请填写所有必填项！');
                return;
            }

            // 显示提交动画
            const submitBtn = document.querySelector('.submit-btn');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
            submitBtn.disabled = true;
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化星级评分
            setupRating('taste-rating', 'taste_rating_input');
            setupRating('hygiene-rating', 'hygiene_rating_input');
            setupRating('service-rating', 'service_rating_input');

            // 添加表单区域动画
            const sections = document.querySelectorAll('.form-section, .rating-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    section.style.transition = 'all 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 智能时间设置
            const mealTypeSelect = document.getElementById('meal_type');
            const timeInput = document.getElementById('dining_time');

            mealTypeSelect.addEventListener('change', function() {
                switch(this.value) {
                    case 'breakfast':
                        timeInput.value = '07:30';
                        break;
                    case 'lunch':
                        timeInput.value = '12:00';
                        break;
                    case 'dinner':
                        timeInput.value = '18:00';
                        break;
                }
            });
        });
    </script>
</body>
</html>
