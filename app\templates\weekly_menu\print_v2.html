<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 打印版</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* A4横向打印设置 */
        @page {
            size: A4 landscape;
            margin: 1.5cm 1cm;
        }

        html, body {
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: white;
            width: 100%;
            height: 100%;
        }

        .container {
            width: 100%;
            max-width: none;
            padding: 0;
            margin: 0;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #000;
        }

        .subtitle {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }

        .print-info {
            font-size: 11px;
            color: #666;
            margin-bottom: 15px;
        }

        /* 菜单表格 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
        }

        .menu-table th,
        .menu-table td {
            border: 1px solid #000;
            padding: 8px 6px;
            vertical-align: top;
            word-wrap: break-word;
        }

        .menu-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            font-size: 13px;
        }

        /* 列宽设置 */
        .date-col {
            width: 12%;
        }

        .meal-col {
            width: 29.33%;
        }

        /* 日期单元格 */
        .date-cell {
            text-align: center;
            font-weight: bold;
            font-size: 11px;
            background-color: #fafafa;
        }

        .date-cell .weekday {
            font-size: 12px;
            margin-bottom: 3px;
        }

        .date-cell .date {
            font-size: 10px;
            color: #666;
        }

        /* 餐次单元格 */
        .meal-cell {
            text-align: left;
            padding: 6px;
        }

        .meal-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .meal-list li {
            margin-bottom: 2px;
            font-size: 11px;
            line-height: 1.3;
        }

        .meal-list li:before {
            content: "• ";
            color: #666;
        }

        .no-meal {
            color: #999;
            font-style: italic;
            font-size: 10px;
        }

        /* 页脚 */
        .footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            font-size: 11px;
            page-break-inside: avoid;
        }

        .signature-section {
            text-align: center;
            min-width: 150px;
        }

        .signature-label {
            margin-bottom: 30px;
            color: #666;
        }

        .signature-line {
            border-bottom: 1px solid #000;
            width: 120px;
            margin: 0 auto;
        }

        /* 打印时隐藏不必要的元素 */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .no-print {
                display: none !important;
            }

            /* 确保表格不被分页 */
            .menu-table {
                page-break-inside: avoid;
            }

            .menu-table tr {
                page-break-inside: avoid;
            }
        }

        /* 屏幕预览样式 */
        @media screen {
            body {
                background: #f5f5f5;
                padding: 20px;
            }

            .container {
                background: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                padding: 20px;
                margin: 0 auto;
                max-width: 1000px;
            }

            .print-button {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                z-index: 1000;
            }

            .print-button:hover {
                background: #0056b3;
            }
        }
    </style>
</head>
<body>
    <!-- 打印按钮（仅在屏幕上显示） -->
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> 打印
    </button>

    <div class="container">
        <!-- 页眉 -->
        <div class="header">
            <div class="title">{{ weekly_menu.area.name }}周菜单计划表</div>
            <div class="subtitle">
                {{ weekly_menu.week_start.strftime('%Y年%m月%d日') }} 至 {{ weekly_menu.week_end.strftime('%Y年%m月%d日') }}
            </div>
            <div class="print-info">
                打印时间：<span id="current-time"></span>
            </div>
        </div>

        <!-- 菜单表格 -->
        <table class="menu-table">
            <thead>
                <tr>
                    <th class="date-col">日期</th>
                    <th class="meal-col">早餐</th>
                    <th class="meal-col">午餐</th>
                    <th class="meal-col">晚餐</th>
                </tr>
            </thead>
            <tbody>
                {% for date_str, day_data in week_data.items() %}
                <tr>
                    <td class="date-cell">
                        <div class="weekday">{{ day_data.weekday }}</div>
                        <div class="date">{{ date_str[5:] }}</div>
                    </td>
                    {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                    <td class="meal-cell">
                        {% if day_data.meals[meal_type] %}
                        <ul class="meal-list">
                            {% for recipe in day_data.meals[meal_type] %}
                            <li>{{ recipe.name }}</li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <div class="no-meal">未安排</div>
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- 页脚 -->
        <div class="footer">
            <div class="signature-section">
                <div class="signature-label">制定人：</div>
                <div class="signature-line"></div>
            </div>
            <div class="signature-section">
                <div class="signature-label">审核人：</div>
                <div class="signature-line"></div>
            </div>
            <div class="signature-section">
                <div class="signature-label">负责人：</div>
                <div class="signature-line"></div>
            </div>
        </div>
    </div>

    <script>
        // 设置当前时间
        function setCurrentTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');

            const timeString = `${year}年${month}月${day}日 ${hours}:${minutes}`;
            document.getElementById('current-time').textContent = timeString;
        }

        // 自动打印功能（可选）
        function autoPrint() {
            if (window.location.search.includes('auto_print=1')) {
                setTimeout(() => {
                    window.print();
                }, 1000);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            setCurrentTime();
            autoPrint();
        });
    </script>
</body>
</html>
